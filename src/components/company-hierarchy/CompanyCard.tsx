"use client";

import React from "react";
import { Building2, Upload, Settings } from "lucide-react";
import { Company } from "./CompanyTreeView";

interface CompanyCardProps {
  company: Company;
  onAddSubCompany: (
    name: string,
    description: string,
    contactDatabase: string,
    parentId?: string
  ) => void;
  isSelected: boolean;
  onSelect: () => void;
  level: number;
  theme?: "light" | "dark";
}

export function CompanyCard({
  company,
  onAddSubCompany,
  isSelected,
  onSelect,
  level,
  theme = "dark",
}: CompanyCardProps) {
  return (
    <div className="cursor-pointer group" onClick={onSelect}>
      <div
        className={`relative group cursor-grab active:cursor-grabbing transition-all duration-300 ${
          isSelected ? "scale-105" : "hover:scale-105"
        }`}
        style={{
          cursor: "grab",
          filter: isSelected
            ? "drop-shadow(0 0 30px rgba(16, 185, 129, 0.6))"
            : undefined,
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.filter =
            "drop-shadow(0 0 40px rgba(16, 185, 129, 0.8))";
        }}
        onMouseLeave={(e) => {
          if (!isSelected) {
            e.currentTarget.style.filter = "";
          }
        }}
      >
        {/* Glass Card - Theme-aware Design */}
        <div
          className="relative overflow-hidden"
          style={{
            width: "371.2px",
            height: "200px",
            borderRadius: "25.6px",
            background:
              theme === "light"
                ? "rgba(255, 255, 255, 0.95)"
                : "rgba(255, 255, 255, 0.03)",
            border:
              theme === "light"
                ? "1.2px solid rgba(16, 185, 129, 0.2)"
                : "1.2px solid transparent",
            backgroundImage:
              theme === "light"
                ? "linear-gradient(158.39deg, rgba(255, 255, 255, 0.98) 14.19%, rgba(240, 249, 245, 0.95) 50.59%, rgba(255, 255, 255, 0.98) 68.79%, rgba(240, 249, 245, 0.95) 105.18%)"
                : "linear-gradient(158.39deg, rgba(255, 255, 255, 0.06) 14.19%, rgba(255, 255, 255, 0.000015) 50.59%, rgba(255, 255, 255, 0.000015) 68.79%, rgba(255, 255, 255, 0.015) 105.18%)",
            backgroundOrigin: "border-box",
            backgroundClip: "padding-box, border-box",
            padding: "12.8px",
            backdropFilter: "blur(20px)",
            WebkitBackdropFilter: "blur(20px)",
            boxShadow:
              theme === "light"
                ? "0 8px 32px rgba(16, 185, 129, 0.12), 0 1px 0 rgba(255, 255, 255, 0.8) inset"
                : undefined,
          }}
        >
          {/* Animated glow dots in corners */}
          <div
            className="absolute top-4 left-4 w-1 h-1 bg-emerald-400 rounded-full animate-pulse opacity-60"
            style={{ boxShadow: "0 0 8px #10b981" }}
          />
          <div
            className="absolute top-8 left-2 w-0.5 h-0.5 bg-emerald-300 rounded-full animate-pulse opacity-40"
            style={{ boxShadow: "0 0 4px #6ee7b7" }}
          />
          <div
            className="absolute bottom-12 left-6 w-0.5 h-0.5 bg-emerald-400 rounded-full animate-pulse opacity-50"
            style={{ boxShadow: "0 0 6px #10b981" }}
          />
          <div
            className="absolute bottom-4 left-2 w-1 h-1 bg-emerald-300 rounded-full animate-pulse opacity-30"
            style={{ boxShadow: "0 0 8px #6ee7b7" }}
          />

          {/* Main content layout */}
          <div className="relative z-10 h-full flex items-center gap-6">
            {/* Left side - Circular icon area */}
            <div className="flex-shrink-0">
              {/* Outer glow ring */}
              <div
                className="relative w-24 h-24 rounded-full border border-emerald-400/30"
                style={{
                  background:
                    "radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)",
                  boxShadow:
                    "0 0 20px rgba(16, 185, 129, 0.2), inset 0 0 20px rgba(16, 185, 129, 0.1)",
                }}
              >
                {/* Inner icon container */}
                <div
                  className="absolute inset-2 rounded-full bg-emerald-600 flex items-center justify-center"
                  style={{
                    background:
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                    boxShadow:
                      "0 0 15px rgba(16, 185, 129, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1)",
                  }}
                >
                  {/* Building icon */}
                  <Building2 className="text-white w-6 h-6" />

                  {/* Settings icon on top */}
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <Settings className="w-4 h-4 text-emerald-300" />
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Text content */}
            <div className="flex-1">
              <h2
                className={`text-2xl font-semibold mb-3 tracking-wide ${
                  theme === "light" ? "text-gray-800" : "text-white"
                }`}
              >
                {company.name}
              </h2>
              <p
                className={`text-base leading-relaxed ${
                  theme === "light" ? "text-gray-600" : "text-gray-300"
                }`}
              >
                {company.description}
              </p>
            </div>
          </div>



          {/* Upload button - positioned in bottom right */}
          <button
            className="absolute bottom-4 right-4 flex items-center gap-2 text-emerald-400 text-sm hover:text-emerald-300 transition-colors z-20"
            onClick={(e) => e.stopPropagation()}
          >
            <Upload className="w-4 h-4" />
            <span>Upload</span>
          </button>

          {/* Connection points */}
          <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 z-30">
            <div className="w-4 h-4 rounded-full border-2 border-emerald-500 bg-gray-800 flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse" />
            </div>
          </div>

          {level > 0 && (
            <div className="absolute -left-2 top-1/2 transform -translate-y-1/2 z-30">
              <div className="w-4 h-4 rounded-full border-2 border-emerald-500 bg-gray-800 flex items-center justify-center">
                <div className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse" />
              </div>
            </div>
          )}

          {/* Hover glow effect */}
          <div
            className={`absolute inset-0 rounded-[25.6px] transition-all duration-500 pointer-events-none ${
              isSelected ? "opacity-30" : "opacity-0 group-hover:opacity-20"
            }`}
            style={{
              background:
                "radial-gradient(circle at 30% 50%, rgba(16, 185, 129, 0.2) 0%, transparent 70%)",
            }}
          />
        </div>
      </div>
    </div>
  );
}
