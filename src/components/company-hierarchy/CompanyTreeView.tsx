"use client";

import React, { useCallback, useMemo, useState, useEffect } from "react";
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MarkerType,
} from "reactflow";
import "reactflow/dist/style.css";
import { Building2, Plus, ChevronDown } from "lucide-react";
import { CompanyCreationModal, CompanyFormData } from "./CompanyCreationModal";
import { ParentCompanyService } from "@/lib/api/services/parent-company-service";
import { SubCompanyService } from "@/lib/api/services/sub-company-service";
import { ParentCompanyData, SubCompanyData } from "@/types/api";
import { toast } from "sonner";

// Types
export interface Company {
  id: string;
  name: string;
  description?: string;
  address?: string;
  contactEmail?: string;
  dbId: number;
  parentId?: string;
  children?: Company[];
}

interface CompanyTreeViewProps {
  onCompanyCreated?: () => void;
}

// Custom Node Component for companies with SystemCard design
const CompanyNode = ({ data, selected }: { data: any; selected: boolean }) => {
  const isMainCompany = data.level === 0;

  return (
    <div className="relative">
      {/* Dynamic connection points with pulsing animation */}
      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-30">
        <div className="w-3 h-3 rounded-full border-2 border-emerald-400 bg-black flex items-center justify-center">
          <div
            className="w-1.5 h-1.5 rounded-full bg-emerald-400 animate-pulse"
            style={{ boxShadow: "0 0 8px #10b981" }}
          />
        </div>
      </div>

      {/* Bottom connection point - only for parent companies */}
      {isMainCompany && (
        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 z-30">
          <div className="w-3 h-3 rounded-full border-2 border-emerald-400 bg-black flex items-center justify-center">
            <div
              className="w-1.5 h-1.5 rounded-full bg-emerald-400 animate-pulse"
              style={{ boxShadow: "0 0 8px #10b981" }}
            />
          </div>
        </div>
      )}

      {/* Main Card with SystemCard design */}
      <div
        className={`relative group cursor-pointer transition-all duration-300 ${
          selected ? "scale-105" : "hover:scale-105"
        }`}
        style={{
          filter: selected
            ? "drop-shadow(0 0 30px rgba(16, 185, 129, 0.6))"
            : undefined,
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.filter =
            "drop-shadow(0 0 40px rgba(16, 185, 129, 0.8))";
        }}
        onMouseLeave={(e) => {
          if (!selected) {
            e.currentTarget.style.filter = "";
          }
        }}
        onClick={() => data.onSelect?.(data.company.id)}
      >
        {/* Glass Card - SystemCard Design */}
        <div
          className="relative overflow-hidden"
          style={{
            width: isMainCompany ? "400px" : "350px",
            height: "200px",
            borderRadius: "25.6px",
            background: "rgba(255, 255, 255, 0.03)",
            border: "1.2px solid transparent",
            backgroundImage:
              "linear-gradient(158.39deg, rgba(255, 255, 255, 0.06) 14.19%, rgba(255, 255, 255, 0.000015) 50.59%, rgba(255, 255, 255, 0.000015) 68.79%, rgba(255, 255, 255, 0.015) 105.18%)",
            backgroundOrigin: "border-box",
            backgroundClip: "padding-box, border-box",
            padding: "12.8px",
            backdropFilter: "blur(20px)",
            WebkitBackdropFilter: "blur(20px)",
          }}
        >
          {/* Animated glow dots in corners */}
          <div
            className="absolute top-4 left-4 w-1 h-1 bg-emerald-400 rounded-full animate-pulse opacity-60"
            style={{ boxShadow: "0 0 8px #10b981" }}
          />
          <div
            className="absolute top-8 left-2 w-0.5 h-0.5 bg-emerald-300 rounded-full animate-pulse opacity-40"
            style={{ boxShadow: "0 0 4px #6ee7b7" }}
          />
          <div
            className="absolute bottom-12 left-6 w-0.5 h-0.5 bg-emerald-400 rounded-full animate-pulse opacity-50"
            style={{ boxShadow: "0 0 6px #10b981" }}
          />
          <div
            className="absolute bottom-4 left-2 w-1 h-1 bg-emerald-300 rounded-full animate-pulse opacity-30"
            style={{ boxShadow: "0 0 8px #6ee7b7" }}
          />

          {/* Main content layout */}
          <div className="relative z-10 h-full flex items-center gap-6">
            {/* Left side - Circular icon area */}
            <div className="flex-shrink-0">
              <div
                className="relative w-24 h-24 rounded-full border border-emerald-400/30"
                style={{
                  background:
                    "radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)",
                  boxShadow:
                    "0 0 20px rgba(16, 185, 129, 0.2), inset 0 0 20px rgba(16, 185, 129, 0.1)",
                }}
              >
                <div
                  className="absolute inset-2 rounded-full bg-emerald-600 flex items-center justify-center"
                  style={{
                    background:
                      "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                    boxShadow:
                      "0 0 15px rgba(16, 185, 129, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1)",
                  }}
                >
                  <Building2 className="text-white w-6 h-6" />

                  {/* Status indicator on top */}
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                    <div
                      className="w-4 h-4 rounded-full bg-emerald-300 flex items-center justify-center"
                      style={{ boxShadow: "0 0 8px #6ee7b7" }}
                    >
                      <div className="w-2 h-2 rounded-full bg-white animate-pulse" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Text content */}
            <div className="flex-1">
              <h2 className="text-white text-2xl font-semibold mb-3 tracking-wide">
                {data.company.name}
              </h2>
              <p className="text-gray-300 text-base leading-relaxed">
                {data.company.description || "Company description"}
              </p>
              {data.company.address && (
                <p className="text-gray-400 text-sm mt-2">
                  📍 {data.company.address}
                </p>
              )}
            </div>
          </div>

          {/* Selected state indicator */}
          {selected && (
            <div className="absolute top-4 right-4 flex items-center gap-2">
              <div
                className="w-2 h-2 rounded-full bg-emerald-400 animate-pulse"
                style={{ boxShadow: "0 0 8px #10b981" }}
              />
              <span className="text-emerald-400 text-xs font-medium">
                ACTIVE
              </span>
            </div>
          )}

          {/* Add Sub-Company button when selected and is main company */}
          {selected && isMainCompany && (
            <div className="absolute bottom-4 right-4">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  data.onAddSubCompany(data.company.id);
                }}
                className="px-3 py-1.5 bg-emerald-500/20 border border-emerald-400/50 rounded-lg text-emerald-400 text-sm hover:bg-emerald-500/30 transition-all duration-200 flex items-center gap-2"
              >
                <Plus className="w-3 h-3" />
                Add Sub-Company
              </button>
            </div>
          )}

          {/* Hover glow effect */}
          <div
            className={`absolute inset-0 rounded-[25.6px] transition-all duration-500 pointer-events-none ${
              selected ? "opacity-30" : "opacity-0 group-hover:opacity-20"
            }`}
            style={{
              background:
                "radial-gradient(circle at 30% 50%, rgba(16, 185, 129, 0.2) 0%, transparent 70%)",
            }}
          />
        </div>
      </div>

      {/* Plus button at bottom - only for parent companies */}
      {isMainCompany && (
        <div
          className="absolute z-20 cursor-pointer"
          style={{
            left: "50%",
            top: "220px",
            transform: "translateX(-50%)",
          }}
          onClick={(e) => {
            e.stopPropagation();
            data.onAddSubCompany(data.company.id);
          }}
        >
          <div
            className="w-10 h-10 rounded-full bg-emerald-500/20 border-2 border-emerald-400 flex items-center justify-center hover:bg-emerald-500/30 hover:scale-110 transition-all duration-200 shadow-lg"
            style={{ boxShadow: "0 0 20px rgba(16, 185, 129, 0.3)" }}
          >
            <Plus className="w-5 h-5 text-emerald-400" />
          </div>
        </div>
      )}
    </div>
  );
};

// Empty State Component - Floating Plus Icon
const EmptyStateNode = ({ data }: { data: any }) => {
  return (
    <div className="relative">
      <div
        className="w-16 h-16 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center hover:bg-green-500/30 transition-all duration-300 cursor-pointer shadow-2xl shadow-green-400/20"
        onClick={data.onAddParentCompany}
        style={{
          boxShadow: "0 0 30px rgba(34, 197, 94, 0.3)",
        }}
      >
        <Plus className="w-8 h-8 text-green-400" />
      </div>
      <div className="absolute top-20 left-1/2 transform -translate-x-1/2 text-center">
        <p className="text-green-400 text-sm font-medium">
          Create Parent Company
        </p>
        <p className="text-gray-400 text-xs mt-1">Click to get started</p>
      </div>
    </div>
  );
};

const nodeTypes = {
  company: CompanyNode,
  emptyState: EmptyStateNode,
};

export function CompanyTreeView({ onCompanyCreated }: CompanyTreeViewProps) {
  // State management
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);
  const [selectedParentForFlow, setSelectedParentForFlow] = useState<
    string | null
  >(null);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState<"parent" | "sub">("parent");
  const [parentCompanyId, setParentCompanyId] = useState<number | undefined>();

  // Load companies on mount
  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async () => {
    setLoading(true);
    try {
      // Load parent companies and sub companies separately to handle errors better
      let parentCompanies: ParentCompanyData[] = [];
      let subCompanies: SubCompanyData[] = [];

      try {
        const parentResponse = await ParentCompanyService.getParentCompanies();
        console.log("Parent companies response:", parentResponse);
        // Handle different possible response structures
        if ((parentResponse as any).companies) {
          parentCompanies = (parentResponse as any).companies;
        } else if ((parentResponse as any).data?.companies) {
          parentCompanies = (parentResponse as any).data.companies;
        } else if (Array.isArray(parentResponse)) {
          parentCompanies = parentResponse as any;
        } else {
          console.log(
            "Parent response structure:",
            Object.keys(parentResponse as any)
          );
        }
      } catch (parentError) {
        console.error("Error loading parent companies:", parentError);
        // Continue with empty parent companies array
      }

      try {
        const subResponse = await SubCompanyService.getSubCompanies();
        console.log("Sub companies response:", subResponse);
        // Handle different possible response structures
        if ((subResponse as any).companies) {
          subCompanies = (subResponse as any).companies;
        } else if ((subResponse as any).data?.companies) {
          subCompanies = (subResponse as any).data.companies;
        } else if (Array.isArray(subResponse)) {
          subCompanies = subResponse as any;
        } else {
          console.log(
            "Sub response structure:",
            Object.keys(subResponse as any)
          );
        }
      } catch (subError) {
        console.error("Error loading sub companies:", subError);
        // Continue with empty sub companies array
      }

      console.log("Extracted parent companies:", parentCompanies);
      console.log("Extracted sub companies:", subCompanies);
      console.log("Parent companies length:", parentCompanies.length);
      console.log("Sub companies length:", subCompanies.length);

      // Transform API data to our Company interface
      const transformedCompanies: Company[] = parentCompanies.map(
        (parent: ParentCompanyData) => ({
          id: parent.parent_company_id.toString(),
          name: parent.company_name,
          description: parent.description,
          address: parent.address,
          contactEmail: parent.contact_email,
          dbId: parent.db_id,
          children: subCompanies
            .filter(
              (sub: SubCompanyData) =>
                sub.parent_company_id === parent.parent_company_id
            )
            .map((sub: SubCompanyData) => ({
              id: sub.sub_company_id.toString(),
              name: sub.company_name,
              description: sub.description,
              address: sub.address,
              contactEmail: sub.contact_email,
              dbId: sub.db_id,
              parentId: sub.parent_company_id.toString(),
            })),
        })
      );

      console.log("Transformed companies:", transformedCompanies);
      setCompanies(transformedCompanies);
    } catch (error) {
      console.error("Error loading companies:", error);
      toast.error("Failed to load companies");
      setCompanies([]); // Set empty array as fallback
    } finally {
      setLoading(false);
    }
  };

  // Create nodes and edges for React Flow
  const { nodes, edges } = useMemo(() => {
    if (companies.length === 0) {
      // Empty state - show floating plus icon in center
      return {
        nodes: [
          {
            id: "empty-state",
            type: "emptyState",
            position: { x: 400, y: 300 },
            data: {
              onAddParentCompany: () => {
                setModalType("parent");
                setParentCompanyId(undefined);
                setModalOpen(true);
              },
            },
            draggable: false,
          },
        ],
        edges: [],
      };
    }

    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];

    // If a parent company is selected for flow view, show only that branch
    if (selectedParentForFlow) {
      const selectedParent = companies.find(
        (c) => c.id === selectedParentForFlow
      );
      if (selectedParent) {
        // Add main company node (centered)
        flowNodes.push({
          id: selectedParent.id,
          type: "company",
          position: { x: 400, y: 150 },
          data: {
            company: selectedParent,
            level: 0,
            onSelect: (companyId: string) => {
              setSelectedCompany(
                companyId === selectedCompany ? null : companyId
              );
            },
            onAddSubCompany: (parentId: string) => {
              setModalType("sub");
              setParentCompanyId(parseInt(parentId));
              setModalOpen(true);
            },
          },
          selected: selectedCompany === selectedParent.id,
          draggable: false,
        });

        // Add child nodes
        if (selectedParent.children && selectedParent.children.length > 0) {
          const childY = 450;
          const spacing = 320;
          const startX =
            400 - ((selectedParent.children.length - 1) * spacing) / 2;

          selectedParent.children.forEach((child, index) => {
            const childX = startX + index * spacing;

            flowNodes.push({
              id: child.id,
              type: "company",
              position: { x: childX, y: childY },
              data: {
                company: child,
                level: 1,
                onSelect: (companyId: string) => {
                  setSelectedCompany(
                    companyId === selectedCompany ? null : companyId
                  );
                },
                onAddSubCompany: (parentId: string) => {
                  setModalType("sub");
                  setParentCompanyId(parseInt(parentId));
                  setModalOpen(true);
                },
              },
              selected: selectedCompany === child.id,
              draggable: false,
            });

            // Add beautiful dynamic edge from parent to child
            flowEdges.push({
              id: `${selectedParent.id}-${child.id}`,
              source: selectedParent.id,
              target: child.id,
              type: "smoothstep",
              style: {
                stroke: "#10b981",
                strokeWidth: 6,
                strokeDasharray: "10,5",
                filter: "drop-shadow(0 0 8px #10b981) drop-shadow(0 0 16px rgba(16, 185, 129, 0.6))",
                opacity: 1,
              },
              markerEnd: {
                type: MarkerType.ArrowClosed,
                color: "#10b981",
                width: 25,
                height: 25,
              },
              animated: true,
              label: "connects to",
              labelStyle: {
                fill: "#10b981",
                fontWeight: 600,
                fontSize: "12px",
              },
              labelBgStyle: {
                fill: "rgba(16, 185, 129, 0.1)",
                fillOpacity: 0.8,
              },
            });
          });
        }
      }
    } else {
      // Show all companies or first company (original behavior)
      const mainCompany = companies[0];

      // Add main company node
      flowNodes.push({
        id: mainCompany.id,
        type: "company",
        position: { x: 200, y: 150 },
        data: {
          company: mainCompany,
          level: 0,
          onSelect: (companyId: string) => {
            setSelectedCompany(
              companyId === selectedCompany ? null : companyId
            );
          },
          onAddSubCompany: (parentId: string) => {
            setModalType("sub");
            setParentCompanyId(parseInt(parentId));
            setModalOpen(true);
          },
        },
        selected: selectedCompany === mainCompany.id,
        draggable: false,
      });

      // Add child nodes
      if (mainCompany.children && mainCompany.children.length > 0) {
        const childY = 450;
        const spacing = 320;
        const startX = 200 - ((mainCompany.children.length - 1) * spacing) / 2;

        mainCompany.children.forEach((child, index) => {
          const childX = startX + index * spacing;

          flowNodes.push({
            id: child.id,
            type: "company",
            position: { x: childX, y: childY },
            data: {
              company: child,
              level: 1,
              onSelect: (companyId: string) => {
                setSelectedCompany(
                  companyId === selectedCompany ? null : companyId
                );
              },
              onAddSubCompany: (parentId: string) => {
                setModalType("sub");
                setParentCompanyId(parseInt(parentId));
                setModalOpen(true);
              },
            },
            selected: selectedCompany === child.id,
            draggable: false,
          });

          // Add beautiful dynamic edge from parent to child
          flowEdges.push({
            id: `${mainCompany.id}-${child.id}`,
            source: mainCompany.id,
            target: child.id,
            type: "smoothstep",
            style: {
              stroke: "#10b981",
              strokeWidth: 6,
              strokeDasharray: "10,5",
              filter: "drop-shadow(0 0 8px #10b981) drop-shadow(0 0 16px rgba(16, 185, 129, 0.6))",
              opacity: 1,
            },
            markerEnd: {
              type: MarkerType.ArrowClosed,
              color: "#10b981",
              width: 25,
              height: 25,
            },
            animated: true,
            label: "connects to",
            labelStyle: {
              fill: "#10b981",
              fontWeight: 600,
              fontSize: "12px",
            },
            labelBgStyle: {
              fill: "rgba(16, 185, 129, 0.1)",
              fillOpacity: 0.8,
            },
          });
        });
      }
    }

    return { nodes: flowNodes, edges: flowEdges };
  }, [companies, selectedCompany, selectedParentForFlow]);

  const [flowNodes, setNodes, onNodesChange] = useNodesState([]);
  const [flowEdges, setEdges, onEdgesChange] = useEdgesState([]);

  // Update nodes and edges when they change
  useEffect(() => {
    setNodes(nodes);
    setEdges(edges);
  }, [nodes, edges, setNodes, setEdges]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle company creation
  const handleCompanySubmit = async (companyData: CompanyFormData) => {
    try {
      if (modalType === "parent") {
        await ParentCompanyService.createParentCompany({
          company_name: companyData.name,
          description: companyData.description,
          address: companyData.address,
          contact_email: companyData.contactEmail,
          db_id: companyData.dbId,
        });
      } else {
        if (!parentCompanyId) {
          throw new Error(
            "Parent company ID is required for sub-company creation"
          );
        }
        await SubCompanyService.createSubCompany({
          company_name: companyData.name,
          description: companyData.description,
          address: companyData.address,
          contact_email: companyData.contactEmail,
          db_id: companyData.dbId,
          parent_company_id: parentCompanyId,
        });
      }

      // Reload companies after creation
      await loadCompanies();
      onCompanyCreated?.();
    } catch (error) {
      console.error("Error creating company:", error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen w-full relative overflow-hidden flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="text-white text-xl">Loading companies...</div>
      </div>
    );
  }

  return (
    <>
      {/* Custom styles for enhanced edge visibility */}
      <style jsx>{`
        .react-flow__edge-path {
          stroke: #10b981 !important;
          stroke-width: 6px !important;
          filter: drop-shadow(0 0 8px #10b981) drop-shadow(0 0 16px rgba(16, 185, 129, 0.4)) !important;
          opacity: 1 !important;
        }

        .react-flow__edge.animated .react-flow__edge-path {
          stroke-dasharray: 10, 5 !important;
          animation: dashdraw 2s linear infinite !important;
        }

        @keyframes dashdraw {
          to {
            stroke-dashoffset: -15px;
          }
        }

        .react-flow__arrowhead {
          fill: #10b981 !important;
          filter: drop-shadow(0 0 4px #10b981) !important;
        }
      `}</style>

      <div className="min-h-screen w-full relative overflow-hidden bg-black">
        {/* Custom grid background overlay */}
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            backgroundImage: `
              linear-gradient(rgba(34, 197, 94, 0.08) 1px, transparent 1px),
              linear-gradient(90deg, rgba(34, 197, 94, 0.08) 1px, transparent 1px)
            `,
            backgroundSize: "40px 40px",
            zIndex: 1,
          }}
        />

        {/* SVG Definitions for gradients and effects */}
        <svg width="0" height="0" style={{ position: "absolute" }}>
          <defs>
            <linearGradient
              id="emerald-gradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" stopColor="#10b981" stopOpacity="0.8" />
              <stop offset="50%" stopColor="#34d399" stopOpacity="1" />
              <stop offset="100%" stopColor="#10b981" stopOpacity="0.8" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>
        </svg>

        {/* Floating particles background */}
        <div className="absolute inset-0 pointer-events-none z-1">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-emerald-400/20 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`,
                boxShadow: "0 0 4px rgba(16, 185, 129, 0.3)",
              }}
            />
          ))}
        </div>

        {/* React Flow Container */}
        <div
          style={{
            width: "100%",
            height: "100vh",
            position: "relative",
            zIndex: 2,
          }}
        >
          <ReactFlow
            nodes={flowNodes}
            edges={flowEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            className="bg-transparent"
            proOptions={{ hideAttribution: true }}
            defaultEdgeOptions={{
              animated: true,
              style: {
                stroke: '#10b981',
                strokeWidth: 6,
                filter: 'drop-shadow(0 0 8px #10b981)',
              },
              markerEnd: {
                type: MarkerType.ArrowClosed,
                color: '#10b981',
                width: 20,
                height: 20,
              },
            }}
          >
            {/* Controls (optional - can be hidden) */}
            <Controls className="opacity-50" />
          </ReactFlow>
        </div>

        {/* Right sidebar with company tree - only show if we have companies */}
        {companies.length > 0 && (
          <div className="absolute top-20 right-6 w-80 bg-gray-900/95 backdrop-blur-md border border-green-400/30 rounded-2xl p-6 z-40 shadow-2xl shadow-green-400/10">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-lg bg-green-500/20 border border-green-400/40 flex items-center justify-center">
                  <Building2 className="w-4 h-4 text-green-400" />
                </div>
                <h3 className="text-white font-semibold text-lg">Companies</h3>
              </div>
              {selectedParentForFlow && (
                <button
                  onClick={() => setSelectedParentForFlow(null)}
                  className="px-2 py-1 bg-green-500/20 border border-green-400/50 rounded text-green-400 text-xs hover:bg-green-500/30 transition-colors"
                >
                  Show All
                </button>
              )}
            </div>

            {/* Selection indicator */}
            {selectedParentForFlow && (
              <div className="mb-4 p-2 bg-green-500/10 border border-green-400/30 rounded text-green-400 text-sm">
                <span className="font-medium">Viewing: </span>
                {companies.find((c) => c.id === selectedParentForFlow)?.name}
              </div>
            )}

            {/* File Tree Structure */}
            <div className="space-y-0 font-mono text-sm">
              {companies.map((company) => (
                <div key={company.id}>
                  {/* Parent company - folder style */}
                  <div
                    className={`flex items-center gap-2 cursor-pointer p-2 hover:bg-gray-800/50 transition-colors ${
                      selectedParentForFlow === company.id
                        ? "bg-green-500/20 border-l-2 border-green-400"
                        : ""
                    }`}
                    onClick={() => {
                      // Toggle selection - if same company is clicked, deselect
                      if (selectedParentForFlow === company.id) {
                        setSelectedParentForFlow(null);
                      } else {
                        setSelectedParentForFlow(company.id);
                      }
                    }}
                  >
                    {/* Folder icon */}
                    <span className="text-green-400 text-base">📁</span>
                    {/* Green dot */}
                    <div className="w-2 h-2 rounded-full bg-green-400" />
                    <span className="text-white font-medium">
                      {company.name}
                    </span>
                  </div>

                  {/* Children - file tree structure */}
                  {company.children && company.children.length > 0 && (
                    <div>
                      {company.children.map((child, index) => {
                        const isLast = index === company.children!.length - 1;
                        return (
                          <div key={child.id} className="relative">
                            {/* Tree connector lines */}
                            <div className="absolute left-4 top-0 flex items-center h-10">
                              {/* Vertical line */}
                              {!isLast && (
                                <div className="absolute left-0 top-5 w-px h-5 bg-green-400/40" />
                              )}
                              {/* L-shaped connector */}
                              <div className="flex">
                                <div className="w-px h-5 bg-green-400/40" />
                                <div className="w-4 h-px bg-green-400/40 mt-5" />
                              </div>
                            </div>

                            <div
                              className={`flex items-center gap-2 cursor-pointer p-2 ml-6 hover:bg-gray-800/40 transition-colors ${
                                selectedCompany === child.id
                                  ? "bg-green-500/15"
                                  : ""
                              }`}
                              onClick={() => setSelectedCompany(child.id)}
                            >
                              {/* File icon */}
                              <span className="text-green-400/70 text-sm">
                                📄
                              </span>
                              {/* Green dot */}
                              <div className="w-2 h-2 rounded-full bg-green-400/70" />
                              <span className="text-gray-300">
                                {child.name}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Company Creation Modal */}
      <CompanyCreationModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleCompanySubmit}
        type={modalType}
        parentCompanyId={parentCompanyId}
      />
    </>
  );
}

// Export the main component as default
export default CompanyTreeView;
