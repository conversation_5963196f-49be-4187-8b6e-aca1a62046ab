"use client";

import React, { useCallback, useMemo } from "react";
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
} from "reactflow";
import "reactflow/dist/style.css";
import { Building2, Plus, ChevronDown } from "lucide-react";

// Types
interface Company {
  id: string;
  name: string;
  description?: string;
  children?: Company[];
}

interface CompanyTreeViewProps {
  companies: Company[];
  selectedCompany: string | null;
  onSelectCompany: (id: string | null) => void;
}

// Custom Node Component matching the exact design
const CompanyNode = ({ data, selected }: { data: any; selected: boolean }) => {
  const isMainCompany = data.level === 0;

  return (
    <div className="relative">
      {/* Plus button at top center of main card */}
      {isMainCompany && (
        <div
          className="absolute z-20 cursor-pointer"
          style={{
            left: "50%",
            top: "-16px",
            transform: "translateX(-50%)",
          }}
        >
          <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center hover:bg-green-500/30 transition-colors">
            <Plus className="w-4 h-4 text-green-400" />
          </div>
        </div>
      )}

      {/* Main Card */}
      <div
        className={`relative bg-gray-800/95 backdrop-blur-md border-2 rounded-2xl p-6 transition-all duration-300 cursor-pointer ${
          isMainCompany
            ? "w-80 h-48 border-green-400/80 shadow-2xl shadow-green-400/20"
            : "w-72 h-40 border-green-500/30 hover:border-green-400/40"
        } ${selected ? "ring-2 ring-green-400/50" : ""}`}
        onClick={() => data.onSelect(data.company.id)}
      >
        {/* Animated glow dots in corners */}
        <div
          className="absolute top-4 left-4 w-1 h-1 bg-emerald-400 rounded-full animate-pulse opacity-60"
          style={{ boxShadow: "0 0 8px #10b981" }}
        />
        <div
          className="absolute top-8 left-2 w-0.5 h-0.5 bg-emerald-300 rounded-full animate-pulse opacity-40"
          style={{ boxShadow: "0 0 4px #6ee7b7" }}
        />
        <div
          className="absolute bottom-12 left-6 w-0.5 h-0.5 bg-emerald-400 rounded-full animate-pulse opacity-50"
          style={{ boxShadow: "0 0 6px #10b981" }}
        />
        <div
          className="absolute bottom-4 left-2 w-1 h-1 bg-emerald-300 rounded-full animate-pulse opacity-30"
          style={{ boxShadow: "0 0 8px #6ee7b7" }}
        />

        {/* Icon and content */}
        <div className="flex items-center gap-4">
          {/* Left side - Icon */}
          <div className="flex-shrink-0">
            <div
              className="w-16 h-16 rounded-full border border-emerald-400/30 flex items-center justify-center relative"
              style={{
                background:
                  "radial-gradient(circle, rgba(16, 185, 129, 0.1) 0%, transparent 70%)",
                boxShadow: "0 0 20px rgba(16, 185, 129, 0.2)",
              }}
            >
              <div
                className="w-12 h-12 rounded-full bg-emerald-600 flex items-center justify-center"
                style={{
                  background:
                    "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                }}
              >
                <span className="text-white font-bold text-xs">API</span>
              </div>

              {/* Gear icon on top */}
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="text-emerald-300"
                >
                  <path
                    d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5a3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97c0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1c0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"
                    fill="currentColor"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Right side - Text content */}
          <div className="flex-1">
            <h3 className="text-white font-semibold text-lg mb-2">
              {data.company.name}
            </h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              {data.company.description ||
                "Automate refund processes with configurable policy enforcement."}
            </p>
          </div>
        </div>

        {/* Upload button */}
        {isMainCompany && (
          <div className="absolute bottom-4 right-4">
            <button className="flex items-center gap-2 text-green-400 text-sm bg-green-500/10 px-3 py-1 rounded-full border border-green-500/30 hover:border-green-400/50 transition-colors">
              Upload
            </button>
          </div>
        )}
      </div>

      {/* Plus button at bottom */}
      <div
        className="absolute z-20 cursor-pointer"
        style={{
          left: isMainCompany ? "135px" : "125px",
          top: isMainCompany ? "200px" : "170px",
        }}
      >
        <div className="w-8 h-8 rounded-full bg-green-500/20 border-2 border-green-400 flex items-center justify-center hover:bg-green-500/30 transition-colors">
          <Plus className="w-4 h-4 text-green-400" />
        </div>
      </div>
    </div>
  );
};

const nodeTypes = {
  company: CompanyNode,
};

export function CompanyTreeView({
  companies,
  selectedCompany,
  onSelectCompany,
}: CompanyTreeViewProps) {
  // Create nodes and edges for React Flow
  const { nodes, edges } = useMemo(() => {
    if (companies.length === 0) return { nodes: [], edges: [] };

    const mainCompany = companies[0]; // Meta is the first company
    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];

    // Add Meta node (positioned in center-left)
    flowNodes.push({
      id: mainCompany.id,
      type: "company",
      position: { x: 200, y: 150 },
      data: {
        company: mainCompany,
        level: 0,
        onSelect: onSelectCompany,
      },
      draggable: false,
    });

    // Add child nodes (positioned below Meta)
    if (mainCompany.children && mainCompany.children.length > 0) {
      const childY = 450;
      const spacing = 320;
      const startX = 200 - ((mainCompany.children.length - 1) * spacing) / 2;

      mainCompany.children.forEach((child, index) => {
        const childX = startX + index * spacing;

        flowNodes.push({
          id: child.id,
          type: "company",
          position: { x: childX, y: childY },
          data: {
            company: child,
            level: 1,
            onSelect: onSelectCompany,
          },
          draggable: false,
        });

        // Add edge from parent to child
        flowEdges.push({
          id: `${mainCompany.id}-${child.id}`,
          source: mainCompany.id,
          target: child.id,
          type: "straight",
          style: {
            stroke: "rgba(34, 197, 94, 0.6)",
            strokeWidth: 2,
          },
          animated: true,
        });
      });
    }

    return { nodes: flowNodes, edges: flowEdges };
  }, [companies, onSelectCompany]);

  const [flowNodes, setNodes, onNodesChange] = useNodesState([]);
  const [flowEdges, setEdges, onEdgesChange] = useEdgesState([]);

  // Update nodes and edges when they change
  React.useEffect(() => {
    setNodes(nodes);
    setEdges(edges);
  }, [nodes, edges, setNodes, setEdges]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Show loading state if no companies
  if (companies.length === 0) {
    return (
      <div className="min-h-screen w-full relative overflow-hidden flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <div className="text-white text-xl">Loading companies...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full relative overflow-hidden bg-black">
      {/* Custom grid background overlay */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          backgroundImage: `
            linear-gradient(rgba(34, 197, 94, 0.08) 1px, transparent 1px),
            linear-gradient(90deg, rgba(34, 197, 94, 0.08) 1px, transparent 1px)
          `,
          backgroundSize: "40px 40px",
          zIndex: 1,
        }}
      />

      {/* React Flow Container */}
      <div
        style={{
          width: "100%",
          height: "100vh",
          position: "relative",
          zIndex: 2,
        }}
      >
        <ReactFlow
          nodes={flowNodes}
          edges={flowEdges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          className="bg-transparent"
          proOptions={{ hideAttribution: true }}
        >
          {/* Controls (optional - can be hidden) */}
          <Controls className="opacity-50" />
        </ReactFlow>
      </div>

      {/* Right sidebar with company tree matching the green theme design */}
      <div className="absolute top-20 right-6 w-80 bg-gray-900/95 backdrop-blur-md border border-green-400/30 rounded-2xl p-6 z-40 shadow-2xl shadow-green-400/10">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-lg bg-green-500/20 border border-green-400/40 flex items-center justify-center">
              <Building2 className="w-4 h-4 text-green-400" />
            </div>
            <h3 className="text-white font-semibold text-lg">Meta</h3>
          </div>
          <ChevronDown className="w-4 h-4 text-green-400" />
        </div>

        {/* File Tree Structure */}
        <div className="space-y-0 font-mono text-sm">
          {companies.map((company) => (
            <div key={company.id}>
              {/* Parent company - folder style */}
              <div
                className={`flex items-center gap-2 cursor-pointer p-2 hover:bg-gray-800/50 transition-colors ${
                  selectedCompany === company.id ? "bg-green-500/20" : ""
                }`}
                onClick={() => onSelectCompany(company.id)}
              >
                {/* Folder icon */}
                <span className="text-green-400 text-base">📁</span>
                {/* Green dot */}
                <div className="w-2 h-2 rounded-full bg-green-400" />
                <span className="text-white font-medium">{company.name}</span>
              </div>

              {/* Children - file tree structure */}
              {company.children && company.children.length > 0 && (
                <div>
                  {company.children.map((child, index) => {
                    const isLast = index === company.children!.length - 1;
                    return (
                      <div key={child.id} className="relative">
                        {/* Tree connector lines */}
                        <div className="absolute left-4 top-0 flex items-center h-10">
                          {/* Vertical line */}
                          {!isLast && (
                            <div className="absolute left-0 top-5 w-px h-5 bg-green-400/40" />
                          )}
                          {/* L-shaped connector */}
                          <div className="flex">
                            <div className="w-px h-5 bg-green-400/40" />
                            <div className="w-4 h-px bg-green-400/40 mt-5" />
                          </div>
                        </div>

                        <div
                          className={`flex items-center gap-2 cursor-pointer p-2 ml-6 hover:bg-gray-800/40 transition-colors ${
                            selectedCompany === child.id
                              ? "bg-green-500/15"
                              : ""
                          }`}
                          onClick={() => onSelectCompany(child.id)}
                        >
                          {/* File icon */}
                          <span className="text-green-400/70 text-sm">📄</span>
                          {/* Green dot */}
                          <div className="w-2 h-2 rounded-full bg-green-400/70" />
                          <span className="text-gray-300">{child.name}</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Demo component with sample data
export default function CompanyTreeDemo() {
  const [selectedCompany, setSelectedCompany] = React.useState<string | null>(
    null
  );

  const sampleCompanies = [
    {
      id: "meta",
      name: "Meta",
      description:
        "Connect the world through innovative social platforms and virtual reality experiences.",
      children: [
        {
          id: "facebook",
          name: "Facebook",
          description:
            "Social networking platform connecting billions of people worldwide.",
        },
        {
          id: "instagram",
          name: "Instagram",
          description: "Photo and video sharing social networking service.",
        },
        {
          id: "whatsapp",
          name: "WhatsApp",
          description: "Cross-platform messaging and voice over IP service.",
        },
      ],
    },
  ];

  return (
    <CompanyTreeView
      companies={sampleCompanies}
      selectedCompany={selectedCompany}
      onSelectCompany={setSelectedCompany}
    />
  );
}
